[package]
name = "tamtil-core"
version = "0.1.0"
edition.workspace = true
description = "TAMTIL Core - Stable API Type System for Distributed Actor Framework"
license = "MIT"

[features]
default = []
bytecheck = ["dep:bytecheck", "rkyv/bytecheck"]

[dependencies]
async-trait.workspace = true
rkyv.workspace = true
thiserror.workspace = true
tokio = { workspace = true, features = ["full"] }
munge = "0.4"
bytecheck = { version = "0.8", optional = true }
